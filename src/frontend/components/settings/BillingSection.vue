<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <h2 class="card-title">Billing</h2>
      <p class="mt-1 mb-6 text-base-content/70">
        Manage your subscription, payment methods, and additional purchases.
      </p>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-md"></span>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="alert alert-error rounded-lg mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>{{ error }}</span>
        <button @click="loadBillingInfo" class="btn btn-sm btn-outline">
          Try again
        </button>
      </div>

      <!-- Billing Content -->
      <div v-else-if="billingInfo" class="space-y-6">

        <!-- 1. Subscription Management -->
        <div class="bg-base-200/40 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-6">Subscription management</h3>

          <!-- Current Plan -->
          <div class="mb-6">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h4 class="text-base font-medium mb-1">Current plan</h4>
                <div class="flex items-center gap-2">
                  <p class="text-base-content/70">{{ billingInfo.currentPlan.name }}</p>
                  <div
                    :class="[
                      'badge badge-sm',
                      billingInfo.currentPlan.status === 'active' ? 'badge-success' :
                      billingInfo.currentPlan.status === 'cancelled' ? 'badge-error' :
                      'badge-warning'
                    ]"
                  >
                    {{ billingInfo.currentPlan.status || 'active' }}
                  </div>
                </div>
              </div>
              <div class="text-right">
                <div v-if="billingInfo.currentPlan.amount" class="text-2xl font-bold">
                  €{{ billingInfo.currentPlan.amount.value }}
                  <span class="text-sm font-normal text-base-content/70">
                    /{{ billingInfo.currentPlan.interval }}
                  </span>
                </div>
                <div v-else class="text-2xl font-bold text-success">
                  Free
                </div>
              </div>
            </div>

            <!-- Next Payment Info -->
            <div v-if="billingInfo.currentPlan.nextPaymentDate" class="mb-4">
              <span class="text-sm text-base-content/70">
                Next payment: {{ formatDate(billingInfo.currentPlan.nextPaymentDate) }}
              </span>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-2 justify-end pt-4">
              <button
                v-if="billingInfo.currentPlan.type === 'free'"
                @click="showUpgradeModal = true"
                class="btn btn-primary btn-sm w-full md:w-1/3"
              >
                Upgrade plan
              </button>
              <template v-else>
                <!-- <button
                  @click="showUpgradeModal = true"
                  class="btn btn-outline btn-sm"
                >
                  Change plan
                </button> -->
                <button
                  v-if="billingInfo.currentPlan.status === 'active'"
                  @click="showCancelModal = true"
                  class="btn btn-error btn-outline btn-sm w-full md:w-2/5"
                >
                  Cancel subscription
                </button>
              </template>
            </div>
          </div>

          <!-- Grandfathering Badge -->
          <GrandfatheringBadge :grandfathering="billingInfo.grandfathering || null" />
        </div>

        <!-- 2. Usage & Limits (Combined) -->
        <div class="bg-base-200/40 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-6">Usage & limits</h3>

          <!-- Email Usage (Primary/Featured) -->
          <div class="mb-8">
            <h4 class="text-base font-medium mb-4">Email usage</h4>

            <!-- Monthly Allowance -->
            <div class="mb-6">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium">Monthly allowance</span>
                <span class="text-sm text-base-content/70">
                  {{ billingInfo.emailBreakdown?.monthlyUsed || billingInfo.usage.emails }} / {{ billingInfo.emailBreakdown?.monthlyAllowance || billingInfo.limits.emails }}
                </span>
              </div>
              <div class="w-full bg-base-300 rounded-full h-3">
                <div
                  class="h-3 rounded-full transition-all duration-300"
                  :class="getMonthlyUsageColor()"
                  :style="{ width: getMonthlyUsagePercentage() + '%' }"
                ></div>
              </div>
            </div>

            <!-- Purchased Credits (if any) -->
            <div v-if="billingInfo.creditInfo && billingInfo.creditInfo.balance > 0" class="mb-6">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium">Purchased credits</span>
                <span class="text-sm text-base-content/70">
                  {{ billingInfo.creditInfo.balance }} emails
                </span>
              </div>
              <div v-if="billingInfo.creditInfo.expiringCredits > 0" class="text-xs text-warning mb-1">
                {{ billingInfo.creditInfo.expiringCredits }} credits expire in 30 days
              </div>
              <div v-if="billingInfo.creditInfo.nextExpirationDate" class="text-xs text-base-content/50">
                Next expiration: {{ formatDate(billingInfo.creditInfo.nextExpirationDate) }}
              </div>
            </div>

            <!-- Total Available -->
            <div class="bg-base-100 rounded-lg p-4">
              <div class="flex justify-between items-center">
                <span class="font-medium">Total available</span>
                <span class="font-bold text-xl text-primary">
                  {{ billingInfo.emailBreakdown?.totalAvailable || billingInfo.limits.emails }} emails
                </span>
              </div>
            </div>
          </div>

          <!-- Other Resources (Secondary) -->
          <div class="border-t border-base-300 pt-6">
            <h4 class="text-base font-medium mb-4">Other resources</h4>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">

              <div class="text-center">
                <div class="text-xl font-bold">{{ billingInfo.usage.domains }}</div>
                <div class="text-sm text-base-content/70">
                  of {{ billingInfo.limits.domains }} domains
                </div>
                <div class="w-full bg-base-300 rounded-full h-2 mt-2">
                  <div
                    class="bg-primary h-2 rounded-full transition-all duration-300"
                    :style="{ width: getUsagePercentage('domains') + '%' }"
                  ></div>
                </div>
              </div>

              <div class="text-center">
                <div class="text-xl font-bold">{{ billingInfo.usage.webhooks }}</div>
                <div class="text-sm text-base-content/70">
                  of {{ billingInfo.limits.webhooks }} webhooks
                </div>
                <div class="w-full bg-base-300 rounded-full h-2 mt-2">
                  <div
                    class="bg-primary h-2 rounded-full transition-all duration-300"
                    :style="{ width: getUsagePercentage('webhooks') + '%' }"
                  ></div>
                </div>
              </div>

              <div class="text-center">
                <div class="text-xl font-bold">{{ billingInfo.usage.aliases }}</div>
                <div class="text-sm text-base-content/70">
                  of {{ billingInfo.limits.aliases }} aliases
                </div>
                <div class="w-full bg-base-300 rounded-full h-2 mt-2">
                  <div
                    class="bg-primary h-2 rounded-full transition-all duration-300"
                    :style="{ width: getUsagePercentage('aliases') + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 3. Additional Purchases -->
        <div class="bg-base-200/40 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-6">Additional purchases</h3>

          <!-- Email Credits -->
          <div class="flex justify-between items-center mb-4">
            <h4 class="text-base font-medium">Purchase email credits</h4>
            <div class="text-sm text-base-content/70">
              €{{ billingInfo.currentPlan.type === 'pro' ? proCreditPricing.pricePerHundred?.toFixed(2) || '0.80' : freeCreditPricing.pricePerHundred?.toFixed(2) || '1.00' }} per 100 emails
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <!-- Credit packages -->
            <div
              v-for="pkg in creditPackages"
              :key="pkg.credits"
              class="card bg-base-100 shadow cursor-pointer hover:shadow-lg transition-shadow"
              :class="{ 'ring-2 ring-primary': selectedCreditPackage === pkg.credits }"
              @click="selectedCreditPackage = pkg.credits"
            >
              <div class="card-body p-4 text-center">
                <h4 class="font-bold text-lg">{{ pkg.credits }} emails</h4>
                <div class="text-2xl font-bold text-primary">€{{ pkg.price.toFixed(2) }}</div>
                <div class="text-sm text-base-content/70">{{ pkg.pricePerEmail }} per email</div>
              </div>
            </div>
          </div>

          <button
            @click="purchaseCredits"
            :disabled="!selectedCreditPackage || isPurchasingCredits"
            class="btn btn-primary w-full"
          >
            {{ isPurchasingCredits ? 'Processing...' : `Purchase ${selectedCreditPackage} credits` }}
          </button>
        </div>

        <!-- 4. Billing & Payment Management -->
        <div class="bg-base-200/40 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-6">Billing & payment management</h3>

          <!-- Payment Methods -->
          <div class="mb-8">
            <div class="flex items-center justify-between mb-4">
              <h4 class="text-base font-medium">Payment methods</h4>
              <button
                class="btn btn-outline btn-sm"
                @click="showAddPaymentMethodModal = true"
              >
                <span class="hidden sm:inline">Add payment method</span>
                <span class="sm:hidden">Add</span>
              </button>
            </div>

            <div v-if="billingInfo.paymentMethods.length > 0" class="space-y-3">
              <div
                v-for="method in billingInfo.paymentMethods"
                :key="method.id"
                class="flex items-center justify-between p-4 bg-base-100 rounded-lg"
              >
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-primary/20 rounded flex items-center justify-center">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" />
                    </svg>
                  </div>
                  <div>
                    <div class="font-medium">{{ method.description }}</div>
                    <div class="text-sm text-base-content/70">{{ method.type }}</div>
                  </div>
                  <div v-if="method.isDefault" class="badge badge-primary badge-sm">Default</div>
                </div>
                <button class="btn btn-ghost btn-sm text-error">Remove</button>
              </div>
            </div>

            <div v-else class="text-center py-8 text-base-content/70">
              <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
              <p>No payment methods added</p>
              <p class="text-sm">Add a payment method to upgrade your plan</p>
            </div>
          </div>

          <!-- Billing History -->
          <div class="border-t border-base-300 pt-6">
            <h4 class="text-base font-medium mb-4">Recent payments</h4>

            <!-- Recent Payments -->
            <div class="mb-6">
              <div v-if="billingInfo.recentPayments.length > 0" class="space-y-3">
                <div
                  v-for="payment in billingInfo.recentPayments"
                  :key="payment.id"
                  class="flex items-center justify-between p-4 bg-base-100 rounded-lg"
                >
                  <div>
                    <div class="font-medium">{{ payment.description }}</div>
                    <div class="text-sm text-base-content/70">
                      {{ formatDate(payment.paidAt || payment.createdAt) }}
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="font-medium">€{{ payment.amount.value }}</div>
                    <div
                      :class="[
                        'text-sm',
                        payment.status === 'paid' ? 'text-success' :
                        payment.status === 'failed' ? 'text-error' :
                        'text-warning'
                      ]"
                    >
                      {{ payment.status }}
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="text-center py-6 text-base-content/70">
                <svg class="w-8 h-8 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <p class="text-sm">No payment history</p>
              </div>
            </div>

            <!-- Invoices -->
            <div class="border-t border-base-300 pt-6">
              <InvoiceList />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Plan Upgrade Modal -->
    <div v-if="showUpgradeModal" class="modal modal-open">
      <div class="modal-box max-w-md">
        <h3 class="text-lg font-bold mb-6">Choose your plan</h3>

        <!-- Plan Selection -->
        <div class="grid grid-cols-1 gap-6 mb-6">
          <!-- Pro Plan -->
          <div class="card bg-base-200/40 rounded-lg p-4 cursor-pointer border border-base-300" :class="{ 'ring-2 ring-primary': selectedPlan === 'pro' }">
            <div class="card-body">
              <h4 class="card-title text-primary">Pro plan</h4>
              <div class="text-3xl font-bold mb-2">
                €{{ selectedInterval === 'yearly' ? proPlan.price?.yearly?.toFixed(2) || '99.50' : proPlan.price?.monthly?.toFixed(2) || '9.95' }}
                <span class="text-sm font-normal text-base-content/70">
                  /{{ selectedInterval === 'yearly' ? 'year' : 'month' }}
                </span>
              </div>
              <ul class="space-y-2 mb-4">
                <li class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  Up to {{ proPlan.monthlyEmailLimit?.toLocaleString() || '1,000' }} emails/month
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  {{ proPlan.domains?.toLocaleString() || '5' }} domains + {{ proPlan.aliases?.toLocaleString() || '10' }} aliases/ webhooks per domain
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  Custom headers in webhooks
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  S3-compatible attachment storage
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  Discounted credit purchases
                </li>
              </ul>
              <button
                @click="selectedPlan = 'pro'"
                class="btn btn-primary w-full"
                :class="{ 'btn-outline': selectedPlan !== 'pro' }"
              >
                {{ selectedPlan === 'pro' ? 'Selected' : 'Select Pro' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Billing Interval -->
        <div class="mb-6">
          <h4 class="font-semibold mb-3">Billing interval</h4>
          <div class="flex space-x-4">
            <label class="flex items-center cursor-pointer">
              <input
                type="radio"
                v-model="selectedInterval"
                value="monthly"
                class="radio radio-primary mr-2"
              >
              <span>Monthly</span>
            </label>
            <label class="flex items-center cursor-pointer">
              <input
                type="radio"
                v-model="selectedInterval"
                value="yearly"
                class="radio radio-primary mr-2"
              >
              <span>Yearly</span>
              <span class="badge badge-success bg-base-200/40 text-base-content badge-sm ml-2">Save 17%</span>
            </label>
          </div>
        </div>

        <div class="modal-action">
          <button @click="showUpgradeModal = false" class="btn btn-ghost">Cancel</button>
          <button
            @click="proceedToPayment"
            :disabled="!selectedPlan || isProcessingPayment"
            class="btn btn-primary"
          >
            {{ isProcessingPayment ? 'Processing...' : 'Proceed to payment' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Add Payment Method Modal -->
    <div v-if="showAddPaymentMethodModal" class="modal modal-open">
      <div class="modal-box">
        <h3 class="text-lg font-bold mb-4">Add payment method</h3>

        <div class="text-center py-8">
          <p class="text-base-content/70 mb-4">Payment method management coming soon!</p>
          <p class="text-sm text-base-content/50">This will provide secure payment processing.</p>
        </div>

        <div class="modal-action">
          <button @click="showAddPaymentMethodModal = false" class="btn">Close</button>
        </div>
      </div>
    </div>

    <!-- Cancel Subscription Modal -->
    <div v-if="showCancelModal" class="modal modal-open">
      <div class="modal-box max-w-md">
        <h3 class="text-lg font-bold mb-4 text-error">Cancel subscription</h3>

        <div class="space-y-4 mb-6">
          <div class="bg-error/10 border border-error/20 rounded-lg p-4">
            <div class="flex items-start gap-3">
              <svg class="w-5 h-5 text-error mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div>
                <h4 class="font-medium text-error mb-1">Are you sure?</h4>
              </div>
            </div>
          </div>

          <ul class="space-y-2 text-sm">
            <p class="text-sm text-base-content/70">
              Canceling your subscription will:
            </p>
            <li class="flex items-start gap-2">
              <span class="text-error">•</span>
              <span>Downgrade your account to the Free plan at the end of your current billing period</span>
            </li>
            <li class="flex items-start gap-2">
              <span class="text-error">•</span>
              <span>Reduce your email limit to {{ freePlan.monthlyEmailLimit }} emails per month</span>
            </li>
            <li class="flex items-start gap-2">
              <span class="text-error">•</span>
              <span>Remove access to features like custom headers and attachment storage</span>
            </li>
            <li class="flex items-start gap-2">
              <span class="text-error">•</span>
              <span>Limit you to 1 domain, {{ freePlan.aliases }} aliases, and {{ freePlan.webhooks }} webhooks</span>
            </li>
          </ul>

          <div class="bg-base-300/10 border border-base-300/20 rounded-lg p-3">
            <p class="text-sm text-base-content/70">
              <strong>Note:</strong> you'll continue to have Pro access until {{ billingInfo?.currentPlan.nextPaymentDate ? formatDate(billingInfo.currentPlan.nextPaymentDate) : 'the end of your billing period' }}.
            </p>
          </div>
        </div>

        <div class="modal-action">
          <button @click="showCancelModal = false" class="btn btn-ghost">
            <span class="hidden sm:inline">Keep subscription</span>
            <span class="sm:hidden">No, keep</span>
          </button>
          <button
            @click="cancelSubscription"
            :disabled="isCancelling"
            class="btn btn-error"
          >
            <span v-if="isCancelling">Cancelling...</span>
            <span v-if="!isCancelling" class="hidden sm:inline">Yes, cancel subscription</span>
            <span v-if="!isCancelling" class="sm:hidden">Yes, cancel</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import type { BillingInfo } from '../../../shared/types/payment'
import { usePlanConfig } from '../../composables/usePlanConfig'
import InvoiceList from './InvoiceList.vue'
import GrandfatheringBadge from './GrandfatheringBadge.vue'

// State
const billingInfo = ref<BillingInfo | null>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)

// Plan configuration
const { freePlan, proPlan, freeCreditPricing, proCreditPricing, loadPlanConfig } = usePlanConfig()
const showUpgradeModal = ref(false)
const showAddPaymentMethodModal = ref(false)
const showCancelModal = ref(false)
const isCancelling = ref(false)

// Plan selection state
const selectedPlan = ref<'pro' | null>(null)
const selectedInterval = ref<'monthly' | 'yearly'>('monthly')
const isProcessingPayment = ref(false)

// Credit purchase state
const selectedCreditPackage = ref<number>(100)
const isPurchasingCredits = ref(false)

// Computed
const creditPackages = computed(() => {
  if (!billingInfo.value) return []

  const isPro = billingInfo.value.currentPlan.type === 'pro'
  const pricePerHundred = isPro ? 0.80 : 1.00

  return [
    {
      credits: 100,
      price: pricePerHundred,
      pricePerEmail: '€' + (pricePerHundred / 100).toFixed(3)
    },
    {
      credits: 500,
      price: pricePerHundred * 5,
      pricePerEmail: '€' + (pricePerHundred / 100).toFixed(3)
    },
    {
      credits: 1000,
      price: pricePerHundred * 10,
      pricePerEmail: '€' + (pricePerHundred / 100).toFixed(3)
    }
  ]
})

// Methods
const loadBillingInfo = async () => {
  try {
    isLoading.value = true
    error.value = null

    console.log('Loading billing info from /api/billing/info...')

    // Add timeout to prevent hanging requests
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 15000) // 15 second timeout

    const response = await fetch('/api/billing/info', {
      credentials: 'include',
      signal: controller.signal
    })

    clearTimeout(timeoutId)
    console.log('Billing API response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      const errorMessage = errorData.message || `HTTP ${response.status}: ${response.statusText}`
      throw new Error(errorMessage)
    }

    const data = await response.json()
    console.log('Billing API response data:', data)

    if (data.success) {
      billingInfo.value = data.data
    } else {
      throw new Error(data.message || 'Invalid response format')
    }
  } catch (err: any) {
    if (err.name === 'AbortError') {
      error.value = 'Request timed out. Please check your connection and try again.'
      console.error('Billing info loading timed out')
    } else {
      error.value = err.message
      console.error('Failed to load billing info:', err)
    }
  } finally {
    isLoading.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getUsagePercentage = (type: keyof BillingInfo['usage']) => {
  if (!billingInfo.value) return 0
  const usage = billingInfo.value.usage[type]
  const limit = billingInfo.value.limits[type]
  return Math.min((usage / limit) * 100, 100)
}



const getMonthlyUsagePercentage = () => {
  if (!billingInfo.value) return 0

  // Use emailBreakdown if available, otherwise fall back to basic usage/limits
  if (billingInfo.value.emailBreakdown) {
    const { monthlyUsed, monthlyAllowance } = billingInfo.value.emailBreakdown
    return Math.min((monthlyUsed / monthlyAllowance) * 100, 100)
  }

  // Fallback to basic usage data
  const usage = billingInfo.value.usage.emails
  const limit = billingInfo.value.limits.emails
  return Math.min((usage / limit) * 100, 100)
}

const getMonthlyUsageColor = () => {
  const percentage = getMonthlyUsagePercentage()
  if (percentage >= 90) return 'text-error bg-error'
  if (percentage >= 75) return 'text-warning bg-warning'
  return 'text-success bg-success'
}

const proceedToPayment = async () => {
  if (!selectedPlan.value) return

  try {
    isProcessingPayment.value = true

    console.log('Creating payment for plan:', selectedPlan.value, 'interval:', selectedInterval.value)

    // Add timeout for payment creation
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout

    const response = await fetch('/api/billing/payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      signal: controller.signal,
      body: JSON.stringify({
        planType: selectedPlan.value,
        interval: selectedInterval.value
      })
    })

    clearTimeout(timeoutId)
    console.log('Payment API response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      const errorMessage = errorData.message || `Failed to create payment (HTTP ${response.status})`
      throw new Error(errorMessage)
    }

    const data = await response.json()
    console.log('Payment API response data:', data)

    if (data.success && data.payment?.checkoutUrl) {
      console.log('Redirecting to checkout:', data.payment.checkoutUrl)
      // Redirect to Mollie checkout
      window.location.href = data.payment.checkoutUrl
    } else {
      throw new Error(data.message || 'Invalid payment response - no checkout URL provided')
    }
  } catch (err: any) {
    console.error('Payment creation failed:', err)

    let userMessage = 'Failed to create payment. Please try again.'
    if (err.name === 'AbortError') {
      userMessage = 'Payment creation timed out. Please check your connection and try again.'
    } else if (err.message.includes('HTTP 402')) {
      userMessage = 'Payment required. Please add a payment method first.'
    } else if (err.message.includes('HTTP 403')) {
      userMessage = 'You do not have permission to create payments.'
    } else if (err.message) {
      userMessage = err.message
    }

    alert(userMessage)
  } finally {
    isProcessingPayment.value = false
  }
}

const purchaseCredits = async () => {
  if (!selectedCreditPackage.value) return

  try {
    isPurchasingCredits.value = true

    console.log('Purchasing credits:', selectedCreditPackage.value)

    // Add timeout for credit purchase
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout

    const response = await fetch('/api/billing/credits', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      signal: controller.signal,
      body: JSON.stringify({
        creditAmount: selectedCreditPackage.value
      })
    })

    clearTimeout(timeoutId)
    console.log('Credit purchase API response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      const errorMessage = errorData.message || `Failed to create credit purchase (HTTP ${response.status})`
      throw new Error(errorMessage)
    }

    const data = await response.json()
    console.log('Credit purchase API response data:', data)

    if (data.success && data.payment?.checkoutUrl) {
      console.log('Redirecting to credit purchase checkout:', data.payment.checkoutUrl)
      // Redirect to Mollie checkout
      window.location.href = data.payment.checkoutUrl
    } else {
      throw new Error(data.message || 'Invalid credit purchase response - no checkout URL provided')
    }
  } catch (err: any) {
    console.error('Credit purchase failed:', err)

    let userMessage = 'Failed to create credit purchase. Please try again.'
    if (err.name === 'AbortError') {
      userMessage = 'Credit purchase timed out. Please check your connection and try again.'
    } else if (err.message.includes('HTTP 402')) {
      userMessage = 'Payment required. Please add a payment method first.'
    } else if (err.message.includes('HTTP 403')) {
      userMessage = 'You do not have permission to purchase credits.'
    } else if (err.message) {
      userMessage = err.message
    }

    alert(userMessage)
  } finally {
    isPurchasingCredits.value = false
  }
}

const cancelSubscription = async () => {
  try {
    isCancelling.value = true

    console.log('Cancelling subscription...')

    // Add timeout for cancellation
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout

    const response = await fetch('/api/billing/subscription', {
      method: 'DELETE',
      credentials: 'include',
      signal: controller.signal
    })

    clearTimeout(timeoutId)
    console.log('Cancellation API response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      const errorMessage = errorData.message || `Failed to cancel subscription (HTTP ${response.status})`
      throw new Error(errorMessage)
    }

    const data = await response.json()
    console.log('Cancellation API response data:', data)

    if (data.success) {
      // Refresh billing info to show updated status
      await loadBillingInfo()
      showCancelModal.value = false

      // Show success message
      alert('Subscription cancelled successfully. You\'ll continue to have Pro access until the end of your billing period.')
    } else {
      throw new Error(data.message || 'Failed to cancel subscription')
    }
  } catch (err: any) {
    console.error('Subscription cancellation failed:', err)

    let userMessage = 'Failed to cancel subscription. Please try again or contact support.'
    if (err.name === 'AbortError') {
      userMessage = 'Cancellation request timed out. Please check your connection and try again.'
    } else if (err.message.includes('HTTP 404')) {
      userMessage = 'No active subscription found to cancel.'
    } else if (err.message.includes('HTTP 403')) {
      userMessage = 'You do not have permission to cancel this subscription.'
    } else if (err.message) {
      userMessage = err.message
    }

    alert(userMessage)
  } finally {
    isCancelling.value = false
  }
}

onMounted(async () => {
  await Promise.all([
    loadBillingInfo(),
    loadPlanConfig()
  ])
})
</script>
