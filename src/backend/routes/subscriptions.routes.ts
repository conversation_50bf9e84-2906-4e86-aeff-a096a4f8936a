import { FastifyPluginAsync } from 'fastify';
import { billingAuth } from '../lib/auth.js';
import { paymentWorkflowService } from '../services/payment/payment-workflow.service.js';
import { logger } from '../utils/logger.js';

export const subscriptionsRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get user's subscriptions
  fastify.get('/subscriptions', {
    preHandler: [billingAuth.read()],
    schema: {
      tags: ['Subscriptions'],
      summary: 'Get user subscriptions',
      description: 'Retrieve list of subscriptions for the authenticated user',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            subscriptions: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  mollieId: { type: 'string' },
                  status: { type: 'string' },
                  planType: { type: 'string' },
                  interval: { type: 'string' },
                  amount: {
                    type: 'object',
                    properties: {
                      value: { type: 'string' },
                      currency: { type: 'string' }
                    }
                  },
                  description: { type: 'string' },
                  startDate: { type: 'string', format: 'date-time', nullable: true },
                  nextPaymentDate: { type: 'string', format: 'date-time', nullable: true },
                  cancelledAt: { type: 'string', format: 'date-time', nullable: true },
                  cancelReason: { type: 'string', nullable: true },
                  isGrandfathered: { type: 'boolean' },
                  grandfatheredPrice: { type: 'string', nullable: true },
                  grandfatheringReason: { type: 'string', nullable: true },
                  createdAt: { type: 'string', format: 'date-time' }
                }
              }
            }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const subscriptions = await paymentWorkflowService.getUserSubscriptions(user.id);

      return reply.send({
        success: true,
        subscriptions
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to get user subscriptions');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve subscriptions'
      });
    }
  });

  // Get user's payment methods
  fastify.get('/payment-methods', {
    preHandler: [billingAuth.read()],
    schema: {
      tags: ['Payment Methods'],
      summary: 'Get user payment methods',
      description: 'Retrieve list of stored payment methods for the authenticated user',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            paymentMethods: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  mollieId: { type: 'string' },
                  type: { type: 'string' },
                  description: { type: 'string' },
                  isDefault: { type: 'boolean' },
                  cardHolder: { type: 'string', nullable: true },
                  cardNumber: { type: 'string', nullable: true },
                  cardExpiryDate: { type: 'string', nullable: true },
                  createdAt: { type: 'string', format: 'date-time' }
                }
              }
            }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const paymentMethods = await paymentWorkflowService.getUserPaymentMethods(user.id);

      return reply.send({
        success: true,
        paymentMethods
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to get user payment methods');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve payment methods'
      });
    }
  });

  // Create mandate payment to establish payment method
  fastify.post('/mandate-payment', {
    preHandler: [billingAuth.update()],
    schema: {
      tags: ['Payment Methods'],
      summary: 'Create mandate payment',
      description: 'Create a payment to establish a payment method for recurring payments',
      body: {
        type: 'object',
        properties: {
          amount: {
            type: 'object',
            properties: {
              value: { type: 'string' },
              currency: { type: 'string' }
            },
            required: ['value', 'currency']
          },
          description: { type: 'string' },
          successUrl: { type: 'string' },
          cancelUrl: { type: 'string' }
        },
        required: ['amount', 'description', 'successUrl', 'cancelUrl']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            paymentId: { type: 'string' },
            mollieId: { type: 'string' },
            checkoutUrl: { type: 'string' },
            customerId: { type: 'string' }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const body = request.body as any;

      const result = await paymentWorkflowService.createMandatePayment({
        userId: user.id,
        amount: body.amount,
        description: body.description,
        successUrl: body.successUrl,
        cancelUrl: body.cancelUrl
      });

      return reply.send({
        success: true,
        ...result
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to create mandate payment');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to create mandate payment'
      });
    }
  });

  // Sync payment methods from Mollie
  fastify.post('/sync-payment-methods', {
    preHandler: [billingAuth.update()],
    schema: {
      tags: ['Payment Methods'],
      summary: 'Sync payment methods',
      description: 'Sync stored payment methods from Mollie',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            paymentMethods: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  mollieId: { type: 'string' },
                  type: { type: 'string' },
                  description: { type: 'string' },
                  isDefault: { type: 'boolean' }
                }
              }
            }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const paymentMethods = await paymentWorkflowService.syncPaymentMethods(user.id);

      return reply.send({
        success: true,
        paymentMethods
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to sync payment methods');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to sync payment methods'
      });
    }
  });

  // Set default payment method
  fastify.put('/payment-methods/:paymentMethodId/default', {
    preHandler: [billingAuth.update()],
    schema: {
      tags: ['Payment Methods'],
      summary: 'Set default payment method',
      description: 'Set a payment method as the default for the user',
      params: {
        type: 'object',
        properties: {
          paymentMethodId: { type: 'string' }
        },
        required: ['paymentMethodId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const { paymentMethodId } = request.params as { paymentMethodId: string };

      await paymentWorkflowService.setDefaultPaymentMethod(user.id, paymentMethodId);

      return reply.send({
        success: true,
        message: 'Default payment method updated'
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to set default payment method');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to set default payment method'
      });
    }
  });

  // Delete payment method
  fastify.delete('/payment-methods/:paymentMethodId', {
    preHandler: [billingAuth.update()],
    schema: {
      tags: ['Payment Methods'],
      summary: 'Delete payment method',
      description: 'Delete a stored payment method',
      params: {
        type: 'object',
        properties: {
          paymentMethodId: { type: 'string' }
        },
        required: ['paymentMethodId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const { paymentMethodId } = request.params as { paymentMethodId: string };

      await paymentWorkflowService.deletePaymentMethod(user.id, paymentMethodId);

      return reply.send({
        success: true,
        message: 'Payment method deleted successfully'
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to delete payment method');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to delete payment method'
      });
    }
  });

  // Create subscription with stored payment method
  fastify.post('/subscriptions', {
    preHandler: [billingAuth.update()],
    schema: {
      tags: ['Subscriptions'],
      summary: 'Create subscription',
      description: 'Create a subscription with stored payment method',
      body: {
        type: 'object',
        properties: {
          planType: { type: 'string', enum: ['pro', 'enterprise'] },
          interval: { type: 'string', enum: ['monthly', 'yearly'] },
          paymentMethodId: { type: 'string' }
        },
        required: ['planType', 'interval']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            subscription: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                mollieId: { type: 'string' },
                status: { type: 'string' },
                planType: { type: 'string' },
                interval: { type: 'string' },
                amount: { type: 'string' },
                currency: { type: 'string' }
              }
            }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const body = request.body as any;

      const subscription = await paymentWorkflowService.createSubscriptionWithStoredMethod({
        userId: user.id,
        planType: body.planType,
        interval: body.interval,
        paymentMethodId: body.paymentMethodId
      });

      return reply.send({
        success: true,
        subscription: {
          id: subscription.id,
          mollieId: subscription.mollieId,
          status: subscription.status.toLowerCase(),
          planType: subscription.planType,
          interval: subscription.interval,
          amount: subscription.amount.toString(),
          currency: subscription.currency
        }
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to create subscription');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to create subscription'
      });
    }
  });

  // Cancel subscription
  fastify.delete('/subscriptions/:subscriptionId', {
    preHandler: [billingAuth.update()],
    schema: {
      tags: ['Subscriptions'],
      summary: 'Cancel subscription',
      description: 'Cancel a user subscription',
      params: {
        type: 'object',
        properties: {
          subscriptionId: { type: 'string' }
        },
        required: ['subscriptionId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const { subscriptionId } = request.params as { subscriptionId: string };

      await paymentWorkflowService.cancelSubscription(user.id, subscriptionId);

      return reply.send({
        success: true,
        message: 'Subscription cancelled successfully'
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to cancel subscription');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to cancel subscription'
      });
    }
  });
};