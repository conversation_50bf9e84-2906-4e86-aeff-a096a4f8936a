import { FastifyRequest, FastifyReply } from 'fastify';
import { paymentWorkflowService } from '../../services/payment/payment-workflow.service.js';
import { mollieService } from '../../services/payment/mollie.service.js';
import { logger } from '../../utils/logger.js';

interface MollieWebhookBody {
  id: string;
}

export class MollieWebhookController {
  /**
   * Handle Mollie payment webhooks
   */
  async handlePaymentWebhook(request: FastifyRequest, reply: FastifyReply) {
    try {
      const body = request.body as MollieWebhookBody;
      const signature = request.headers['x-mollie-signature'] as string;

      // Verify webhook signature if configured
      if (signature) {
        const rawBody = JSON.stringify(request.body);
        const isValid = mollieService.verifyWebhookSignature(rawBody, signature);
        
        if (!isValid) {
          logger.warn({ 
            signature,
            paymentId: body.id 
          }, 'Invalid Mollie webhook signature');
          
          return reply.code(401).send({
            statusCode: 401,
            error: 'Unauthorized',
            message: 'Invalid webhook signature'
          });
        }
      }

      // Validate webhook payload
      if (!body.id) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Missing payment ID in webhook payload'
        });
      }

      // Process the payment webhook
      await paymentWorkflowService.processPaymentWebhook(body.id);

      logger.info({ 
        paymentId: body.id 
      }, 'Mollie payment webhook processed successfully');

      return reply.code(200).send({ success: true });

    } catch (error: any) {
      logger.error({
        error: error.message,
        stack: error.stack,
        body: request.body
      }, 'Failed to process Mollie payment webhook');

      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to process webhook'
      });
    }
  }

  /**
   * Handle Mollie subscription webhooks
   */
  async handleSubscriptionWebhook(request: FastifyRequest, reply: FastifyReply) {
    try {
      const body = request.body as MollieWebhookBody;
      const signature = request.headers['x-mollie-signature'] as string;

      // Verify webhook signature if configured
      if (signature) {
        const rawBody = JSON.stringify(request.body);
        const isValid = mollieService.verifyWebhookSignature(rawBody, signature);
        
        if (!isValid) {
          logger.warn({ 
            signature,
            subscriptionId: body.id 
          }, 'Invalid Mollie webhook signature');
          
          return reply.code(401).send({
            statusCode: 401,
            error: 'Unauthorized',
            message: 'Invalid webhook signature'
          });
        }
      }

      // Validate webhook payload
      if (!body.id) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Missing subscription ID in webhook payload'
        });
      }

      // Process the subscription webhook
      await paymentWorkflowService.processSubscriptionWebhook(body.id);

      logger.info({ 
        subscriptionId: body.id 
      }, 'Mollie subscription webhook processed successfully');

      return reply.code(200).send({ success: true });

    } catch (error: any) {
      logger.error({
        error: error.message,
        stack: error.stack,
        body: request.body
      }, 'Failed to process Mollie subscription webhook');

      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to process webhook'
      });
    }
  }
}
