import { prisma } from '../../lib/prisma.js';
import { mollieService } from './mollie.service.js';
import { logger } from '../../utils/logger.js';
import { PlanConfigService } from '../billing/plan-config.service.js';
import { CreditService } from '../billing/credit.service.js';
import { GrandfatheringService } from '../billing/grandfathering.service.js';
import { env } from '../../config/env.js';

export interface CreatePaymentRequest {
  userId: string;
  planType: 'pro' | 'enterprise' | 'credits';
  interval: 'monthly' | 'yearly' | 'one-time';
  successUrl: string;
  cancelUrl: string;
  // Optional overrides for credit purchases
  amount?: number;
  currency?: string;
  description?: string;
  metadata?: Record<string, any>;
}

export interface CreateSubscriptionRequest {
  userId: string;
  planType: 'pro' | 'enterprise';
  interval: 'monthly' | 'yearly';
  mollieCustomerId: string;
}

export class PaymentWorkflowService {
  /**
   * Create or get Mollie customer for user
   */
  async createOrGetCustomer(userId: string) {
    try {
      // Check if user already has a Mollie customer
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { 
          id: true,
          email: true, 
          name: true,
          payments: {
            where: { mollieCustomerId: { not: null } },
            select: { mollieCustomerId: true },
            take: 1
          }
        }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // If user has existing Mollie customer, return it
      if (user.payments.length > 0 && user.payments[0].mollieCustomerId) {
        const existingCustomerId = user.payments[0].mollieCustomerId;
        
        try {
          // Verify customer still exists in Mollie
          await mollieService.getCustomer(existingCustomerId);
          return { customerId: existingCustomerId, isNew: false };
        } catch (error) {
          // Customer doesn't exist in Mollie anymore, create new one
          logger.warn({ 
            userId, 
            customerId: existingCustomerId 
          }, 'Mollie customer not found, creating new one');
        }
      }

      // Create new Mollie customer
      const mollieCustomer = await mollieService.createCustomer({
        name: user.name || user.email,
        email: user.email,
        metadata: {
          userId: userId
        }
      });

      logger.info({
        userId,
        customerId: mollieCustomer.id,
        email: user.email
      }, 'Mollie customer created successfully');

      return { customerId: mollieCustomer.id, isNew: true };
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId
      }, 'Failed to create or get Mollie customer');
      throw error;
    }
  }

  /**
   * Create a mandate payment to establish payment method
   */
  async createMandatePayment(request: {
    userId: string;
    amount: { value: string; currency: string };
    description: string;
    successUrl: string;
    cancelUrl: string;
    metadata?: Record<string, any>;
  }) {
    try {
      // Get or create Mollie customer
      const { customerId } = await this.createOrGetCustomer(request.userId);

      // Create mandate payment
      const molliePayment = await mollieService.createMandatePayment({
        customerId,
        amount: request.amount,
        description: request.description,
        redirectUrl: request.successUrl,
        metadata: {
          userId: request.userId,
          type: 'mandate',
          ...request.metadata
        }
      });

      // Store payment in database
      const payment = await prisma.payment.create({
        data: {
          mollieId: molliePayment.id,
          status: 'PENDING',
          amount: parseFloat(request.amount.value),
          currency: request.amount.currency,
          description: request.description,
          userId: request.userId,
          mollieCustomerId: customerId,
          mollieWebhookData: JSON.parse(JSON.stringify(molliePayment))
        }
      });

      logger.info({
        paymentId: payment.id,
        mollieId: molliePayment.id,
        userId: request.userId,
        customerId
      }, 'Mandate payment created successfully');

      return {
        paymentId: payment.id,
        mollieId: molliePayment.id,
        checkoutUrl: molliePayment.getCheckoutUrl(),
        customerId
      };
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId: request.userId
      }, 'Failed to create mandate payment');
      throw error;
    }
  }

  /**
   * Create a one-time payment for plan upgrade or credit purchase
   */
  async createPayment(request: CreatePaymentRequest) {
    try {
      let amount: number;
      let currency: string;
      let description: string;

      // Handle credit purchases vs plan upgrades
      if (request.planType === 'credits') {
        if (!request.amount || !request.currency || !request.description) {
          throw new Error('Credit purchases require amount, currency, and description');
        }
        amount = request.amount;
        currency = request.currency;
        description = request.description;
      } else {
        // Plan upgrade - get configuration
        const planConfig = PlanConfigService.getPlanConfig(request.planType);
        if (!planConfig.price) {
          throw new Error(`Plan ${request.planType} does not have pricing configured`);
        }

        // Calculate amount based on interval
        amount = request.interval === 'yearly'
          ? planConfig.price.yearly
          : planConfig.price.monthly;
        currency = planConfig.price.currency;
        description = `${planConfig.name} Plan - ${request.interval}`;
      }

      // Get user for metadata
      const user = await prisma.user.findUnique({
        where: { id: request.userId },
        select: { email: true, name: true }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Create payment with Mollie
      const molliePayment = await mollieService.createPayment({
        amount: {
          value: amount.toFixed(2),
          currency
        },
        description,
        redirectUrl: request.successUrl,
        webhookUrl: env.MOLLIE_WEBHOOK_URL,
        metadata: {
          userId: request.userId,
          planType: request.planType,
          interval: request.interval,
          userEmail: user.email,
          ...request.metadata
        }
        // No method specified - let Mollie show all available payment methods
      });

      // Store payment in database
      const payment = await prisma.payment.create({
        data: {
          mollieId: molliePayment.id,
          status: 'PENDING',
          amount: amount,
          currency,
          description,
          userId: request.userId,
          mollieWebhookData: JSON.parse(JSON.stringify(molliePayment))
        }
      });

      logger.info({
        paymentId: payment.id,
        mollieId: molliePayment.id,
        userId: request.userId,
        planType: request.planType,
        amount: amount
      }, 'Payment created successfully');

      return {
        paymentId: payment.id,
        mollieId: molliePayment.id,
        checkoutUrl: molliePayment.getCheckoutUrl(),
        amount: {
          value: amount.toFixed(2),
          currency
        }
      };
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId: request.userId,
        planType: request.planType
      }, 'Failed to create payment');
      throw error;
    }
  }

  /**
   * Create subscription with stored payment method
   */
  async createSubscriptionWithStoredMethod(request: {
    userId: string;
    planType: 'pro' | 'enterprise';
    interval: 'monthly' | 'yearly';
    paymentMethodId?: string; // Optional - uses default if not provided
  }) {
    try {
      // Get or create customer
      const { customerId } = await this.createOrGetCustomer(request.userId);

      // Get payment method to use
      let paymentMethod;
      if (request.paymentMethodId) {
        paymentMethod = await prisma.paymentMethod.findFirst({
          where: { id: request.paymentMethodId, userId: request.userId }
        });
        if (!paymentMethod) {
          throw new Error('Payment method not found');
        }
      } else {
        // Use default payment method
        paymentMethod = await prisma.paymentMethod.findFirst({
          where: { userId: request.userId, isDefault: true }
        });
        if (!paymentMethod) {
          throw new Error('No default payment method found');
        }
      }

      // Get plan configuration
      const planConfig = PlanConfigService.getPlanConfig(request.planType);
      if (!planConfig.price) {
        throw new Error(`Plan ${request.planType} does not have pricing configured`);
      }

      // Check if user has existing grandfathering that should be preserved
      const grandfatheringStatus = await GrandfatheringService.getGrandfatheringStatus(request.userId);
      const shouldKeepGrandfathering = grandfatheringStatus?.isGrandfathered &&
        await GrandfatheringService.validateGrandfathering(request.userId, request.planType, request.interval);

      // Determine effective price (grandfathered or current)
      let amount: number;
      let isGrandfathered = false;
      let grandfatheredPrice: number | null = null;
      let grandfatheringReason: string | null = null;

      if (shouldKeepGrandfathering && grandfatheringStatus) {
        amount = grandfatheringStatus.grandfatheredPrice!;
        isGrandfathered = true;
        grandfatheredPrice = grandfatheringStatus.grandfatheredPrice!;
        grandfatheringReason = grandfatheringStatus.reason || null;

        logger.info({
          userId: request.userId,
          planType: request.planType,
          interval: request.interval,
          grandfatheredPrice: amount,
          currentPrice: grandfatheringStatus.currentPrice,
          savings: grandfatheringStatus.savings
        }, 'User keeps grandfathered pricing');
      } else {
        // Calculate amount based on interval
        amount = request.interval === 'yearly'
          ? planConfig.price.yearly
          : planConfig.price.monthly;
      }

      // Create subscription with Mollie
      const mollieSubscription = await mollieService.createSubscription({
        customerId,
        amount: {
          value: amount.toFixed(2),
          currency: planConfig.price.currency
        },
        interval: request.interval === 'yearly' ? '12 months' : '1 month',
        description: `${planConfig.name} Plan`,
        metadata: {
          userId: request.userId,
          planType: request.planType,
          paymentMethodId: paymentMethod.id
        }
      });

      // Store subscription in database
      const subscription = await prisma.subscription.create({
        data: {
          mollieId: mollieSubscription.id,
          status: 'PENDING',
          planType: request.planType,
          interval: request.interval,
          amount: amount,
          currency: planConfig.price.currency,
          description: `${planConfig.name} Plan`,
          mollieCustomerId: customerId,
          molliePaymentMethod: paymentMethod.mollieId,
          userId: request.userId,
          startDate: new Date(),
          nextPaymentDate: mollieSubscription.nextPaymentDate ? new Date(mollieSubscription.nextPaymentDate) : null,
          // Grandfathering fields
          isGrandfathered,
          grandfatheredPrice,
          grandfatheredAt: isGrandfathered ? new Date() : null,
          grandfatheringReason
        }
      });

      logger.info({
        subscriptionId: subscription.id,
        mollieId: mollieSubscription.id,
        userId: request.userId,
        planType: request.planType,
        paymentMethodId: paymentMethod.id
      }, 'Subscription created with stored payment method');

      return subscription;
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId: request.userId,
        planType: request.planType
      }, 'Failed to create subscription with stored payment method');
      throw error;
    }
  }

  /**
   * Create a recurring subscription
   */
  async createSubscription(request: CreateSubscriptionRequest) {
    try {
      // Get plan configuration
      const planConfig = PlanConfigService.getPlanConfig(request.planType);
      if (!planConfig.price) {
        throw new Error(`Plan ${request.planType} does not have pricing configured`);
      }

      // Check if user has existing grandfathering that should be preserved
      const grandfatheringStatus = await GrandfatheringService.getGrandfatheringStatus(request.userId);
      const shouldKeepGrandfathering = grandfatheringStatus?.isGrandfathered &&
        await GrandfatheringService.validateGrandfathering(request.userId, request.planType, request.interval);

      // Determine effective price (grandfathered or current)
      let amount: number;
      let isGrandfathered = false;
      let grandfatheredPrice: number | null = null;
      let grandfatheringReason: string | null = null;

      if (shouldKeepGrandfathering && grandfatheringStatus) {
        amount = grandfatheringStatus.grandfatheredPrice!;
        isGrandfathered = true;
        grandfatheredPrice = grandfatheringStatus.grandfatheredPrice!;
        grandfatheringReason = grandfatheringStatus.reason || null;

        logger.info({
          userId: request.userId,
          planType: request.planType,
          interval: request.interval,
          grandfatheredPrice: amount,
          currentPrice: grandfatheringStatus.currentPrice,
          savings: grandfatheringStatus.savings
        }, 'User keeps grandfathered pricing');
      } else {
        // Calculate amount based on interval
        amount = request.interval === 'yearly'
          ? planConfig.price.yearly
          : planConfig.price.monthly;
      }

      // Create subscription with Mollie
      const mollieSubscription = await mollieService.createSubscription({
        customerId: request.mollieCustomerId,
        amount: {
          value: amount.toFixed(2),
          currency: planConfig.price.currency
        },
        interval: request.interval === 'yearly' ? '12 months' : '1 month',
        description: `${planConfig.name} Plan`,
        metadata: {
          userId: request.userId,
          planType: request.planType
        }
      });

      // Store subscription in database
      const subscription = await prisma.subscription.create({
        data: {
          mollieId: mollieSubscription.id,
          status: 'PENDING',
          planType: request.planType,
          interval: request.interval,
          amount: amount,
          currency: planConfig.price.currency,
          description: `${planConfig.name} Plan`,
          mollieCustomerId: request.mollieCustomerId,
          userId: request.userId,
          startDate: new Date(),
          nextPaymentDate: mollieSubscription.nextPaymentDate ? new Date(mollieSubscription.nextPaymentDate) : null,
          // Grandfathering fields
          isGrandfathered,
          grandfatheredPrice,
          grandfatheredAt: isGrandfathered ? new Date() : null,
          grandfatheringReason
        }
      });

      logger.info({
        subscriptionId: subscription.id,
        mollieId: mollieSubscription.id,
        userId: request.userId,
        planType: request.planType
      }, 'Subscription created successfully');

      return subscription;
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId: request.userId,
        planType: request.planType
      }, 'Failed to create subscription');
      throw error;
    }
  }

  /**
   * Sync payment methods from Mollie to local database
   */
  async syncPaymentMethods(userId: string) {
    try {
      // Get or create customer
      const { customerId } = await this.createOrGetCustomer(userId);

      // Get payment methods from Mollie
      const molliePaymentMethods = await mollieService.getCustomerPaymentMethods(customerId);

      // Clear existing payment methods for this user
      await prisma.paymentMethod.deleteMany({
        where: { userId }
      });

      // Store new payment methods
      const paymentMethods = [];
      for (const method of molliePaymentMethods) {
        const paymentMethod = await prisma.paymentMethod.create({
          data: {
            mollieId: method.id,
            type: method.method || 'unknown',
            description: method.description || 'Payment method',
            isDefault: false, // We'll set default manually
            cardHolder: method.details?.cardHolder,
            cardNumber: method.details?.cardNumber,
            cardExpiryDate: method.details?.cardExpiryDate,
            cardFingerprint: method.details?.cardFingerprint,
            userId
          }
        });
        paymentMethods.push(paymentMethod);
      }

      // Set first payment method as default if none exists
      if (paymentMethods.length > 0) {
        await prisma.paymentMethod.update({
          where: { id: paymentMethods[0].id },
          data: { isDefault: true }
        });
      }

      logger.info({
        userId,
        customerId,
        paymentMethodCount: paymentMethods.length
      }, 'Payment methods synced successfully');

      return paymentMethods;
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId
      }, 'Failed to sync payment methods');
      throw error;
    }
  }

  /**
   * Get user's stored payment methods
   */
  async getUserPaymentMethods(userId: string) {
    try {
      const paymentMethods = await prisma.paymentMethod.findMany({
        where: { userId },
        orderBy: [
          { isDefault: 'desc' },
          { createdAt: 'desc' }
        ]
      });

      return paymentMethods.map(method => ({
        id: method.id,
        mollieId: method.mollieId,
        type: method.type,
        description: method.description,
        isDefault: method.isDefault,
        cardHolder: method.cardHolder,
        cardNumber: method.cardNumber,
        cardExpiryDate: method.cardExpiryDate,
        createdAt: method.createdAt.toISOString()
      }));
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId
      }, 'Failed to get user payment methods');
      throw error;
    }
  }

  /**
   * Set default payment method for user
   */
  async setDefaultPaymentMethod(userId: string, paymentMethodId: string) {
    try {
      // Verify payment method belongs to user
      const paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: paymentMethodId, userId }
      });

      if (!paymentMethod) {
        throw new Error('Payment method not found');
      }

      // Remove default from all other payment methods
      await prisma.paymentMethod.updateMany({
        where: { userId },
        data: { isDefault: false }
      });

      // Set new default
      await prisma.paymentMethod.update({
        where: { id: paymentMethodId },
        data: { isDefault: true }
      });

      logger.info({
        userId,
        paymentMethodId,
        mollieId: paymentMethod.mollieId
      }, 'Default payment method updated');

      return true;
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId,
        paymentMethodId
      }, 'Failed to set default payment method');
      throw error;
    }
  }

  /**
   * Delete payment method
   */
  async deletePaymentMethod(userId: string, paymentMethodId: string) {
    try {
      // Get payment method and verify ownership
      const paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: paymentMethodId, userId }
      });

      if (!paymentMethod) {
        throw new Error('Payment method not found');
      }

      // Get customer ID for Mollie deletion
      const { customerId } = await this.createOrGetCustomer(userId);

      // Delete from Mollie
      await mollieService.deleteCustomerPaymentMethod(customerId, paymentMethod.mollieId);

      // Delete from database
      await prisma.paymentMethod.delete({
        where: { id: paymentMethodId }
      });

      // If this was the default payment method, set another as default
      if (paymentMethod.isDefault) {
        const nextPaymentMethod = await prisma.paymentMethod.findFirst({
          where: { userId },
          orderBy: { createdAt: 'desc' }
        });

        if (nextPaymentMethod) {
          await prisma.paymentMethod.update({
            where: { id: nextPaymentMethod.id },
            data: { isDefault: true }
          });
        }
      }

      logger.info({
        userId,
        paymentMethodId,
        mollieId: paymentMethod.mollieId
      }, 'Payment method deleted successfully');

      return true;
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId,
        paymentMethodId
      }, 'Failed to delete payment method');
      throw error;
    }
  }

  /**
   * Process payment webhook from Mollie
   */
  async processPaymentWebhook(molliePaymentId: string) {
    try {
      // Get payment details from Mollie
      const molliePayment = await mollieService.getPayment(molliePaymentId);

      // Find payment in database
      const payment = await prisma.payment.findUnique({
        where: { mollieId: molliePaymentId },
        include: { user: true }
      });

      if (!payment) {
        logger.warn({ molliePaymentId }, 'Payment not found in database');
        return;
      }

      // Update payment status
      const updateData: any = {
        status: this.mapMollieStatusToPaymentStatus(molliePayment.status),
        mollieWebhookData: JSON.parse(JSON.stringify(molliePayment))
      };

      // Set timestamps based on status
      if (molliePayment.status === 'paid' && molliePayment.paidAt) {
        updateData.paidAt = new Date(molliePayment.paidAt);
      } else if (molliePayment.status === 'cancelled' && molliePayment.cancelledAt) {
        updateData.cancelledAt = new Date(molliePayment.cancelledAt);
      } else if (molliePayment.status === 'expired' && molliePayment.expiredAt) {
        updateData.expiredAt = new Date(molliePayment.expiredAt);
      } else if (molliePayment.status === 'failed' && molliePayment.failedAt) {
        updateData.failedAt = new Date(molliePayment.failedAt);
        updateData.failureReason = molliePayment.details?.failureReason;
      }

      await prisma.payment.update({
        where: { id: payment.id },
        data: updateData
      });

      // If payment is successful, process based on type
      if (molliePayment.status === 'paid' && molliePayment.metadata) {
        const planType = molliePayment.metadata.planType as string;
        const paymentType = molliePayment.metadata.type as string;

        if (paymentType === 'mandate') {
          // Mandate payment successful - sync payment methods
          await this.syncPaymentMethods(payment.userId);
          logger.info({ paymentId: payment.id }, 'Payment methods synced after mandate payment');
        } else if (planType === 'credits') {
          // Process credit purchase
          await this.processCreditPurchase(payment.id, molliePayment.metadata);
        } else {
          // Process plan upgrade
          await this.upgradeUserPlan(
            payment.userId,
            planType,
            molliePayment.metadata.interval as string
          );
        }

        // Generate invoice for successful payment (skip for mandate payments)
        if (paymentType !== 'mandate') {
          try {
            const { InvoiceGenerationService } = await import('../billing/invoice-generation.service.js');
            await InvoiceGenerationService.createInvoice(payment.id);
            logger.info({ paymentId: payment.id }, 'Invoice generated successfully');
          } catch (invoiceError: any) {
            logger.error({
              paymentId: payment.id,
              error: invoiceError.message
            }, 'Failed to generate invoice');
            // Don't fail the webhook if invoice generation fails
          }
        }
      }

      logger.info({
        paymentId: payment.id,
        mollieId: molliePaymentId,
        status: molliePayment.status,
        userId: payment.userId
      }, 'Payment webhook processed');

    } catch (error: any) {
      logger.error({
        error: error.message,
        molliePaymentId
      }, 'Failed to process payment webhook');
      throw error;
    }
  }

  /**
   * Upgrade user's plan after successful payment
   */
  private async upgradeUserPlan(userId: string, planType: string, interval: string) {
    try {
      const planConfig = PlanConfigService.getPlanConfig(planType);

      await prisma.user.update({
        where: { id: userId },
        data: {
          planType: planType,

        }
      });

      // Sync plan changes to postfix-manager for all user domains
      try {
        const { PostfixManager } = await import('../postfix-manager.js');
        const postfixManager = new PostfixManager();

        await postfixManager.updateUserDomainsPlan(userId, planType);
      } catch (error: any) {
        // Log error but don't fail the plan upgrade
        logger.error({
          error: error.message,
          userId,
          planType
        }, 'Failed to sync plan upgrade to postfix-manager');
      }

      logger.info({
        userId,
        planType,
        interval,
        emailLimit: planConfig.monthlyEmailLimit
      }, 'User plan upgraded successfully');

    } catch (error: any) {
      logger.error({
        error: error.message,
        userId,
        planType
      }, 'Failed to upgrade user plan');
      throw error;
    }
  }

  /**
   * Process credit purchase after successful payment
   */
  private async processCreditPurchase(paymentId: string, metadata: any) {
    try {
      const creditAmount = parseInt(metadata.creditAmount);
      const userId = metadata.userId;

      if (!creditAmount || !userId) {
        throw new Error('Invalid credit purchase metadata');
      }

      // Purchase credits using the credit service
      const result = await CreditService.purchaseCredits(userId, creditAmount, paymentId);

      if (!result.success) {
        throw new Error(result.error || 'Failed to purchase credits');
      }

      logger.info({
        userId,
        creditAmount,
        paymentId,
        batchId: result.batchId,
        newBalance: result.newBalance
      }, 'Credit purchase processed successfully');

    } catch (error: any) {
      logger.error({
        error: error.message,
        paymentId,
        metadata
      }, 'Failed to process credit purchase');
      throw error;
    }
  }

  /**
   * Map Mollie payment status to our payment status enum
   */
  private mapMollieStatusToPaymentStatus(mollieStatus: string): string {
    const statusMap: Record<string, string> = {
      'open': 'PENDING',
      'pending': 'PENDING',
      'paid': 'PAID',
      'cancelled': 'CANCELLED',
      'expired': 'EXPIRED',
      'failed': 'FAILED',
      'authorized': 'AUTHORIZED'
    };

    return statusMap[mollieStatus] || 'PENDING';
  }

  /**
   * Clean up expired pending payments (older than 15 minutes)
   */
  async cleanupExpiredPendingPayments() {
    try {
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
      
      const expiredPayments = await prisma.payment.findMany({
        where: {
          status: 'PENDING',
          createdAt: { lt: fifteenMinutesAgo }
        }
      });

      if (expiredPayments.length > 0) {
        await prisma.payment.updateMany({
          where: {
            status: 'PENDING',
            createdAt: { lt: fifteenMinutesAgo }
          },
          data: {
            status: 'EXPIRED',
            expiredAt: new Date()
          }
        });

        logger.info({ 
          expiredCount: expiredPayments.length 
        }, 'Cleaned up expired pending payments');
      }
    } catch (error: any) {
      logger.error({
        error: error.message
      }, 'Failed to cleanup expired pending payments');
    }
  }

  /**
   * Process subscription webhook from Mollie
   */
  async processSubscriptionWebhook(mollieSubscriptionId: string) {
    try {
      // Find subscription in database
      const subscription = await prisma.subscription.findUnique({
        where: { mollieId: mollieSubscriptionId },
        include: { user: true }
      });

      if (!subscription) {
        logger.warn({ mollieSubscriptionId }, 'Subscription not found in database');
        return;
      }

      // Get subscription details from Mollie
      const mollieSubscription = await mollieService.getSubscription(
        subscription.mollieCustomerId!,
        mollieSubscriptionId
      );

      // Update subscription status
      const updateData: any = {
        status: this.mapMollieStatusToSubscriptionStatus(mollieSubscription.status),
        nextPaymentDate: mollieSubscription.nextPaymentDate ? new Date(mollieSubscription.nextPaymentDate) : null
      };

      // Set timestamps based on status
      if (mollieSubscription.status === 'cancelled' && mollieSubscription.canceledAt) {
        updateData.cancelledAt = new Date(mollieSubscription.canceledAt);
        updateData.cancelReason = 'User cancelled';
      }

      await prisma.subscription.update({
        where: { id: subscription.id },
        data: updateData
      });

      // If subscription is active, update user's plan
      if (mollieSubscription.status === 'active') {
        await this.upgradeUserPlan(subscription.userId, subscription.planType, subscription.interval);
      }

      // If subscription is cancelled, handle plan downgrade
      if (mollieSubscription.status === 'cancelled') {
        await this.handleSubscriptionCancellation(subscription.userId, subscription.planType);
      }

      logger.info({
        subscriptionId: subscription.id,
        mollieId: mollieSubscriptionId,
        status: mollieSubscription.status,
        userId: subscription.userId
      }, 'Subscription webhook processed');

    } catch (error: any) {
      logger.error({
        error: error.message,
        mollieSubscriptionId
      }, 'Failed to process subscription webhook');
      throw error;
    }
  }

  /**
   * Handle subscription cancellation
   */
  private async handleSubscriptionCancellation(userId: string, planType: string) {
    try {
      // For now, we'll downgrade to free plan
      // In future, we might implement grace periods
      await prisma.user.update({
        where: { id: userId },
        data: { planType: 'free' }
      });

      // Sync plan changes to postfix-manager
      try {
        const { PostfixManager } = await import('../postfix-manager.js');
        const postfixManager = new PostfixManager();
        await postfixManager.updateUserDomainsPlan(userId, 'free');
      } catch (error: any) {
        logger.error({
          error: error.message,
          userId
        }, 'Failed to sync plan downgrade to postfix-manager');
      }

      logger.info({
        userId,
        previousPlan: planType,
        newPlan: 'free'
      }, 'User plan downgraded after subscription cancellation');

    } catch (error: any) {
      logger.error({
        error: error.message,
        userId,
        planType
      }, 'Failed to handle subscription cancellation');
      throw error;
    }
  }

  /**
   * Cancel user's subscription
   */
  async cancelSubscription(userId: string, subscriptionId: string) {
    try {
      // Verify subscription belongs to user
      const subscription = await prisma.subscription.findFirst({
        where: { id: subscriptionId, userId }
      });

      if (!subscription) {
        throw new Error('Subscription not found');
      }

      if (subscription.status === 'CANCELLED') {
        throw new Error('Subscription already cancelled');
      }

      // Cancel subscription with Mollie
      const mollieSubscription = await mollieService.cancelSubscription(
        subscription.mollieCustomerId!,
        subscription.mollieId
      );

      // Update subscription in database
      await prisma.subscription.update({
        where: { id: subscriptionId },
        data: {
          status: 'CANCELLED',
          cancelledAt: new Date(),
          cancelReason: 'User cancelled'
        }
      });

      logger.info({
        subscriptionId,
        mollieId: subscription.mollieId,
        userId
      }, 'Subscription cancelled successfully');

      return true;
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId,
        subscriptionId
      }, 'Failed to cancel subscription');
      throw error;
    }
  }

  /**
   * Get user's active subscriptions
   */
  async getUserSubscriptions(userId: string) {
    try {
      const subscriptions = await prisma.subscription.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' }
      });

      return subscriptions.map(sub => ({
        id: sub.id,
        mollieId: sub.mollieId,
        status: sub.status.toLowerCase(),
        planType: sub.planType,
        interval: sub.interval,
        amount: {
          value: sub.amount.toString(),
          currency: sub.currency
        },
        description: sub.description,
        startDate: sub.startDate?.toISOString(),
        nextPaymentDate: sub.nextPaymentDate?.toISOString(),
        cancelledAt: sub.cancelledAt?.toISOString(),
        cancelReason: sub.cancelReason,
        isGrandfathered: sub.isGrandfathered,
        grandfatheredPrice: sub.grandfatheredPrice?.toString(),
        grandfatheringReason: sub.grandfatheringReason,
        createdAt: sub.createdAt.toISOString()
      }));
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId
      }, 'Failed to get user subscriptions');
      throw error;
    }
  }

  /**
   * Map Mollie subscription status to our subscription status enum
   */
  private mapMollieStatusToSubscriptionStatus(mollieStatus: string): string {
    const statusMap: Record<string, string> = {
      'pending': 'PENDING',
      'active': 'ACTIVE',
      'cancelled': 'CANCELLED',
      'suspended': 'SUSPENDED',
      'completed': 'COMPLETED'
    };

    return statusMap[mollieStatus] || 'PENDING';
  }

  /**
   * Get user's payment history
   */
  async getUserPayments(userId: string, limit: number = 10) {
    try {
      const payments = await prisma.payment.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        select: {
          id: true,
          mollieId: true,
          status: true,
          amount: true,
          currency: true,
          description: true,
          method: true,
          paidAt: true,
          createdAt: true
        }
      });

      return payments.map(payment => ({
        id: payment.id,
        amount: {
          value: payment.amount.toString(),
          currency: payment.currency
        },
        description: payment.description || '',
        status: payment.status.toLowerCase(),
        method: payment.method,
        paidAt: payment.paidAt?.toISOString(),
        createdAt: payment.createdAt.toISOString()
      }));
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId
      }, 'Failed to get user payments');
      throw error;
    }
  }
}

export const paymentWorkflowService = new PaymentWorkflowService();
