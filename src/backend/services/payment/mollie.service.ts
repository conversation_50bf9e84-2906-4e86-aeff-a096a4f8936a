import { createMollieClient, MollieApiError, PaymentMethod } from '@mollie/api-client';
import { env } from '../../config/env.js';
import { logger } from '../../utils/logger.js';
import crypto from 'crypto';

export class MollieService {
  private client;

  constructor() {
    if (!env.MOLLIE_API_KEY) {
      logger.warn('MOLLIE_API_KEY not configured - payment processing will be disabled');
      return;
    }

    this.client = createMollieClient({
      apiKey: env.MOLLIE_API_KEY,
    });

    logger.info({ 
      testMode: env.MOLLIE_TEST_MODE 
    }, 'Mollie service initialized');
  }

  /**
   * Check if <PERSON><PERSON> is properly configured
   */
  isConfigured(): boolean {
    return !!this.client && !!env.MOLLIE_API_KEY;
  }

  /**
   * Create a new payment
   */
  async createPayment(params: {
    amount: { value: string; currency: string };
    description: string;
    redirectUrl: string;
    webhookUrl?: string;
    metadata?: Record<string, any>;
    method?: string;
    methods?: string[];
  }) {
    if (!this.isConfigured()) {
      throw new Error('<PERSON>llie service not configured');
    }

    try {
      const paymentData: any = {
        amount: params.amount,
        description: params.description,
        redirectUrl: params.redirectUrl,
        webhookUrl: params.webhookUrl || env.MOLLIE_WEBHOOK_URL,
        metadata: params.metadata,
      };

      // Only set method if explicitly specified
      if (params.method) {
        paymentData.method = params.method;
      }
      // If no method is specified, Mollie will show all available payment methods (both test and production)

      const payment = await this.client.payments.create(paymentData);

      logger.info({ 
        paymentId: payment.id,
        amount: params.amount,
        description: params.description 
      }, 'Payment created successfully');

      return payment;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          error: error.message,
          field: error.field
        }, 'Mollie API error creating payment');
      } else {
        logger.error({ error }, 'Unexpected error creating payment');
      }
      throw error;
    }
  }

  /**
   * Get payment details
   */
  async getPayment(paymentId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const payment = await this.client.payments.get(paymentId);
      return payment;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          paymentId,
          error: error.message,
          field: error.field
        }, 'Mollie API error getting payment');
      } else {
        logger.error({ paymentId, error }, 'Unexpected error getting payment');
      }
      throw error;
    }
  }

  /**
   * Create a customer for recurring payments
   */
  async createCustomer(params: {
    name: string;
    email: string;
    metadata?: Record<string, any>;
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const customer = await this.client.customers.create({
        name: params.name,
        email: params.email,
        metadata: params.metadata,
      });

      logger.info({ 
        customerId: customer.id,
        email: params.email 
      }, 'Customer created successfully');

      return customer;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          error: error.message,
          field: error.field
        }, 'Mollie API error creating customer');
      } else {
        logger.error({ error }, 'Unexpected error creating customer');
      }
      throw error;
    }
  }

  /**
   * Create a subscription
   */
  async createSubscription(params: {
    customerId: string;
    amount: { value: string; currency: string };
    interval: string;
    description: string;
    webhookUrl?: string;
    metadata?: Record<string, any>;
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const subscription = await this.client.customerSubscriptions.create({
        customerId: params.customerId,
        amount: params.amount,
        interval: params.interval,
        description: params.description,
        webhookUrl: params.webhookUrl || env.MOLLIE_WEBHOOK_URL,
        metadata: params.metadata,
      });

      logger.info({ 
        subscriptionId: subscription.id,
        customerId: params.customerId,
        amount: params.amount,
        interval: params.interval 
      }, 'Subscription created successfully');

      return subscription;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          error: error.message,
          field: error.field
        }, 'Mollie API error creating subscription');
      } else {
        logger.error({ error }, 'Unexpected error creating subscription');
      }
      throw error;
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(customerId: string, subscriptionId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const subscription = await this.client.customerSubscriptions.cancel(subscriptionId, {
        customerId,
      });

      logger.info({ 
        subscriptionId,
        customerId 
      }, 'Subscription cancelled successfully');

      return subscription;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          subscriptionId,
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error cancelling subscription');
      } else {
        logger.error({ subscriptionId, customerId, error }, 'Unexpected error cancelling subscription');
      }
      throw error;
    }
  }

  /**
   * Get customer details
   */
  async getCustomer(customerId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const customer = await this.client.customers.get(customerId);
      return customer;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error getting customer');
      } else {
        logger.error({ customerId, error }, 'Unexpected error getting customer');
      }
      throw error;
    }
  }

  /**
   * Update customer details
   */
  async updateCustomer(customerId: string, params: {
    name?: string;
    email?: string;
    metadata?: Record<string, any>;
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const customer = await this.client.customers.update(customerId, params);

      logger.info({ 
        customerId,
        email: params.email 
      }, 'Customer updated successfully');

      return customer;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error updating customer');
      } else {
        logger.error({ customerId, error }, 'Unexpected error updating customer');
      }
      throw error;
    }
  }

  /**
   * Create a payment with stored payment method
   */
  async createPaymentWithStoredMethod(params: {
    customerId: string;
    amount: { value: string; currency: string };
    description: string;
    webhookUrl?: string;
    metadata?: Record<string, any>;
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const payment = await this.client.payments.create({
        customerId: params.customerId,
        amount: params.amount,
        description: params.description,
        webhookUrl: params.webhookUrl || env.MOLLIE_WEBHOOK_URL,
        metadata: params.metadata,
        sequenceType: 'recurring'
      });

      logger.info({ 
        paymentId: payment.id,
        customerId: params.customerId,
        amount: params.amount,
        description: params.description 
      }, 'Payment with stored method created successfully');

      return payment;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId: params.customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error creating payment with stored method');
      } else {
        logger.error({ customerId: params.customerId, error }, 'Unexpected error creating payment with stored method');
      }
      throw error;
    }
  }

  /**
   * Create a one-time payment to establish a customer mandate
   */
  async createMandatePayment(params: {
    customerId: string;
    amount: { value: string; currency: string };
    description: string;
    redirectUrl: string;
    webhookUrl?: string;
    metadata?: Record<string, any>;
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const payment = await this.client.payments.create({
        customerId: params.customerId,
        amount: params.amount,
        description: params.description,
        redirectUrl: params.redirectUrl,
        webhookUrl: params.webhookUrl || env.MOLLIE_WEBHOOK_URL,
        metadata: params.metadata,
        sequenceType: 'first'
      });

      logger.info({ 
        paymentId: payment.id,
        customerId: params.customerId,
        amount: params.amount,
        description: params.description 
      }, 'Mandate payment created successfully');

      return payment;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId: params.customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error creating mandate payment');
      } else {
        logger.error({ customerId: params.customerId, error }, 'Unexpected error creating mandate payment');
      }
      throw error;
    }
  }

  /**
   * Get customer's payment methods
   */
  async getCustomerPaymentMethods(customerId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const paymentMethods = await this.client.customerPaymentMethods.list({
        customerId
      });

      logger.info({ 
        customerId,
        count: paymentMethods.length
      }, 'Customer payment methods retrieved successfully');

      return paymentMethods;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error getting customer payment methods');
      } else {
        logger.error({ customerId, error }, 'Unexpected error getting customer payment methods');
      }
      throw error;
    }
  }

  /**
   * Delete a customer's payment method
   */
  async deleteCustomerPaymentMethod(customerId: string, paymentMethodId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      await this.client.customerPaymentMethods.delete(paymentMethodId, {
        customerId
      });

      logger.info({ 
        customerId,
        paymentMethodId
      }, 'Customer payment method deleted successfully');

      return true;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId,
          paymentMethodId,
          error: error.message,
          field: error.field
        }, 'Mollie API error deleting customer payment method');
      } else {
        logger.error({ customerId, paymentMethodId, error }, 'Unexpected error deleting customer payment method');
      }
      throw error;
    }
  }

  /**
   * Get subscription details
   */
  async getSubscription(customerId: string, subscriptionId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const subscription = await this.client.customerSubscriptions.get(subscriptionId, {
        customerId
      });
      return subscription;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          subscriptionId,
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error getting subscription');
      } else {
        logger.error({ subscriptionId, customerId, error }, 'Unexpected error getting subscription');
      }
      throw error;
    }
  }

  /**
   * Update subscription details
   */
  async updateSubscription(customerId: string, subscriptionId: string, params: {
    amount?: { value: string; currency: string };
    description?: string;
    metadata?: Record<string, any>;
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const subscription = await this.client.customerSubscriptions.update(subscriptionId, {
        customerId,
        ...params
      });

      logger.info({ 
        subscriptionId,
        customerId,
        amount: params.amount
      }, 'Subscription updated successfully');

      return subscription;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          subscriptionId,
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error updating subscription');
      } else {
        logger.error({ subscriptionId, customerId, error }, 'Unexpected error updating subscription');
      }
      throw error;
    }
  }

  /**
   * Verify webhook signature (if webhook secret is configured)
   */
  verifyWebhookSignature(body: string, signature: string): boolean {
    if (!env.MOLLIE_WEBHOOK_SECRET) {
      logger.warn('MOLLIE_WEBHOOK_SECRET not configured - webhook signature verification disabled');
      return true; // Allow webhook if no secret is configured
    }

    const expectedSignature = crypto
      .createHmac('sha256', env.MOLLIE_WEBHOOK_SECRET)
      .update(body)
      .digest('hex');

    const isValid = crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );

    if (!isValid) {
      logger.warn({ signature, expectedSignature }, 'Invalid webhook signature');
    }

    return isValid;
  }
}

// Export singleton instance
export const mollieService = new MollieService();
