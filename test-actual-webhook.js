#!/usr/bin/env node

/**
 * Test script to replicate the exact webhook request that the queue makes
 */

const axios = require('axios');

const webhookUrl = 'https://n8n.axtg.mywire.org:5678/webhook/8e006ec6-9720-4c17-ae7e-5eb4ebbd9328/emailconnect';

// This is the actual payload from the failed delivery
const actualPayload = {
  "message": {
    "date": "2025-07-14T18:19:23.000Z",
    "sender": {
      "name": "Xander Groesbeek",
      "email": "<EMAIL>"
    },
    "content": {
      "html": "<!DOCTYPE html><html><head><title></title></head><body><div style=\"font-family:&quot;Lucida Sans Unicode&quot;;\">Hi!</div><div style=\"font-family:&quot;Lucida Sans Unicode&quot;;\"><br></div><div style=\"font-family:&quot;Lucida Sans Unicode&quot;;\">I'd be curious to learn about your feature roadmap.</div><div style=\"font-family:&quot;Lucida Sans Unicode&quot;;\">What's next?</div><div style=\"font-family:&quot;Lucida Sans Unicode&quot;;\"><br></div><div style=\"font-family:&quot;Lucida Sans Unicode&quot;;\">Thanks!</div><div style=\"font-family:&quot;Lucida Sans Unicode&quot;;\"><br></div><div id=\"sig130706120\"><div class=\"signature\"><div>--</div><div>#️⃣ +31 (0)6 57597089</div><div><br></div></div></div><div style=\"font-family:&quot;Lucida Sans Unicode&quot;;\"><br></div></body></html>",
      "text": "Hi!\n\nI'd be curious to learn about your feature roadmap.\nWhat's next?\n\nThanks!\n\n--\n#️⃣ +31 (0)6 57597089\n\n"
    },
    "subject": "Roadmap",
    "recipient": {
      "name": "support",
      "email": "<EMAIL>"
    },
    "attachments": []
  },
  "envelope": {
    "headers": {
      "to": "{\"value\":[{\"address\":\"<EMAIL>\",\"name\":\"support\"}],\"html\":\"<span class=\\\"mp_address_group\\\"><span class=\\\"mp_address_name\\\">support</span> &lt;<a href=\\\"mailto:<EMAIL>\\\" class=\\\"mp_address_email\\\"><EMAIL></a>&gt;</span>\",\"text\":\"\\\"support\\\" <<EMAIL>>\"}",
      "date": "\"2025-07-14T18:19:23.000Z\"",
      "from": "{\"value\":[{\"address\":\"<EMAIL>\",\"name\":\"Xander Groesbeek\"}],\"html\":\"<span class=\\\"mp_address_group\\\"><span class=\\\"mp_address_name\\\">Xander Groesbeek</span> &lt;<a href=\\\"mailto:<EMAIL>\\\" class=\\\"mp_address_email\\\"><EMAIL></a>&gt;</span>\",\"text\":\"\\\"Xander Groesbeek\\\" <<EMAIL>>\"}",
      "subject": "Roadmap",
      "message-id": "<<EMAIL>>",
      "return-path": "{\"value\":[{\"address\":\"<EMAIL>\",\"name\":\"\"}],\"html\":\"<span class=\\\"mp_address_group\\\"><a href=\\\"mailto:<EMAIL>\\\" class=\\\"mp_address_email\\\"><EMAIL></a></span>\",\"text\":\"<EMAIL>\"}"
    },
    "xMailer": "MessagingEngine.com Webmail Interface",
    "messageId": "<<EMAIL>>",
    "processed": {
      "alias": "<EMAIL>",
      "domain": "in.emailconnect.eu",
      "timestamp": "2025-07-14T18:19:48.264Z",
      "originalSize": 12397
    },
    "returnPath": "{\"value\":[{\"address\":\"<EMAIL>\",\"name\":\"\"}],\"html\":\"<span class=\\\"mp_address_group\\\"><a href=\\\"mailto:<EMAIL>\\\" class=\\\"mp_address_email\\\"><EMAIL></a></span>\",\"text\":\"<EMAIL>\"}",
    "xOriginalTo": "<EMAIL>",
    "allRecipients": {
      "cc": [],
      "to": ["<EMAIL>"],
      "bcc": []
    }
  }
};

// Headers that the queue uses
const headers = {
  'Content-Type': 'application/json',
  'User-Agent': 'EmailConnect/1.0',
  'X-Email-Webhook': 'true'
};

async function testWebhookDelivery() {
  console.log('🚀 Testing webhook delivery with actual payload...');
  console.log('URL:', webhookUrl);
  console.log('Payload size:', JSON.stringify(actualPayload).length, 'bytes');
  
  try {
    const response = await axios.post(webhookUrl, actualPayload, {
      timeout: 30000, // Same timeout as the queue
      headers,
      validateStatus: function (status) {
        return status < 500; // Don't throw for 4xx errors
      }
    });
    
    console.log('✅ Success!');
    console.log('Status:', response.status);
    console.log('Headers:', response.headers);
    console.log('Data:', response.data);
    
  } catch (error) {
    console.error('❌ Failed!');
    console.error('Error:', error.message);
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
      console.error('Data:', error.response.data);
    }
    
    if (error.code) {
      console.error('Error code:', error.code);
    }
  }
}

testWebhookDelivery().catch(console.error);
