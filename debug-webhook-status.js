#!/usr/bin/env node

/**
 * Debug script to check webhook verification status
 * Run this from the n8n-nodes-emailconnect directory
 */

const { PrismaClient } = require('@prisma/client');
const path = require('path');

// Initialize Prisma client pointing to the mailwebhook database
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/eu_email_webhook'
    }
  }
});

async function checkWebhookStatus() {
  try {
    console.log('🔍 Checking webhook verification status...\n');

    // Get all webhooks with their verification status
    const webhooks = await prisma.webhook.findMany({
      select: {
        id: true,
        name: true,
        url: true,
        verified: true,
        active: true,
        createdAt: true,
        updatedAt: true,
        user: {
          select: {
            email: true
          }
        },
        aliases: {
          select: {
            id: true,
            email: true,
            active: true,
            domain: {
              select: {
                domain: true,
                verified: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (webhooks.length === 0) {
      console.log('❌ No webhooks found in database');
      return;
    }

    console.log(`📊 Found ${webhooks.length} webhook(s):\n`);

    webhooks.forEach((webhook, index) => {
      const status = webhook.verified ? '✅ VERIFIED' : '❌ NOT VERIFIED';
      const activeStatus = webhook.active ? '🟢 ACTIVE' : '🔴 INACTIVE';
      
      console.log(`${index + 1}. Webhook: ${webhook.name || 'Unnamed'}`);
      console.log(`   ID: ${webhook.id}`);
      console.log(`   URL: ${webhook.url}`);
      console.log(`   Status: ${status}`);
      console.log(`   Active: ${activeStatus}`);
      console.log(`   User: ${webhook.user?.email || 'Unknown'}`);
      console.log(`   Created: ${webhook.createdAt.toISOString()}`);
      console.log(`   Updated: ${webhook.updatedAt.toISOString()}`);
      
      if (webhook.aliases.length > 0) {
        console.log(`   Aliases:`);
        webhook.aliases.forEach(alias => {
          const aliasStatus = alias.active ? '🟢' : '🔴';
          const domainStatus = alias.domain.verified ? '✅' : '❌';
          console.log(`     ${aliasStatus} ${alias.email} (Domain: ${domainStatus} ${alias.domain.domain})`);
        });
      } else {
        console.log(`   Aliases: None`);
      }
      
      console.log('');
    });

    // Check for unverified webhooks
    const unverifiedWebhooks = webhooks.filter(w => !w.verified);
    if (unverifiedWebhooks.length > 0) {
      console.log(`⚠️  Found ${unverifiedWebhooks.length} unverified webhook(s):`);
      unverifiedWebhooks.forEach(webhook => {
        console.log(`   - ${webhook.name || 'Unnamed'} (${webhook.id})`);
        console.log(`     URL: ${webhook.url}`);
        console.log(`     Last updated: ${webhook.updatedAt.toISOString()}`);
      });
      console.log('\n💡 These webhooks will not receive email payloads until verified.');
    }

    // Check recent emails and their delivery status
    console.log('\n📧 Recent email delivery attempts:');
    const recentEmails = await prisma.email.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        messageId: true,
        fromAddress: true,
        toAddress: true,
        subject: true,
        deliveryStatus: true,
        deliveryError: true,
        createdAt: true,
        domain: {
          select: {
            domain: true
          }
        }
      }
    });

    if (recentEmails.length === 0) {
      console.log('   No recent emails found');
    } else {
      recentEmails.forEach((email, index) => {
        const statusIcon = email.deliveryStatus === 'DELIVERED' ? '✅' : 
                          email.deliveryStatus === 'FAILED' ? '❌' : 
                          email.deliveryStatus === 'RETRYING' ? '🔄' : '⏳';
        
        console.log(`   ${index + 1}. ${statusIcon} ${email.subject || '(no subject)'}`);
        console.log(`      From: ${email.fromAddress}`);
        console.log(`      To: ${email.toAddress}`);
        console.log(`      Domain: ${email.domain?.domain || 'Unknown'}`);
        console.log(`      Status: ${email.deliveryStatus}`);
        if (email.deliveryError) {
          console.log(`      Error: ${email.deliveryError}`);
        }
        console.log(`      Time: ${email.createdAt.toISOString()}`);
        console.log('');
      });
    }

  } catch (error) {
    console.error('❌ Error checking webhook status:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkWebhookStatus().catch(console.error);
