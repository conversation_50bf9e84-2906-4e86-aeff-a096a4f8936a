#!/bin/bash

# Test webhook with production headers
curl -v -X POST https://n8n.axtg.mywire.org:5678/webhook/8e006ec6-9720-4c17-ae7e-5eb4ebbd9328/emailconnect \
  -H "Content-Type: application/json" \
  -H "User-Agent: EmailConnect/1.0" \
  -H "X-Email-Webhook: true" \
  -d '{"message":{"sender":{"email":"<EMAIL>"},"subject":"Test"},"envelope":{"messageId":"test-123"}}' \
  --connect-timeout 30 \
  --max-time 30
