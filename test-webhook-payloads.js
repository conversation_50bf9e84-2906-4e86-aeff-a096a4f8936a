#!/usr/bin/env node

/**
 * Test script to simulate webhook payloads and help debug the verification issue
 * This will help you understand the difference between verification and email payloads
 */

const axios = require('axios');

// Configuration - update these with your actual webhook URL and settings
const WEBHOOK_URL = 'https://your-n8n-webhook-url-here'; // Replace with your actual n8n webhook URL
const BACKEND_URL = 'http://localhost:3000'; // Your EmailConnect backend URL

// Sample verification payload (what you're currently seeing)
const VERIFICATION_PAYLOAD = {
  type: 'webhook_verification',
  verification_token: 'abc12',
  timestamp: Math.floor(Date.now() / 1000),
  webhook: {
    id: 'cmcc8oets000baxhqo9geqd76',
    url: WEBHOOK_URL
  }
};

// Sample email payload (what you should be seeing for actual emails)
const EMAIL_PAYLOAD = {
  message: {
    sender: {
      name: 'Test Sender',
      email: '<EMAIL>'
    },
    recipient: {
      name: null,
      email: '<EMAIL>'
    },
    subject: 'Test Email Subject',
    content: {
      text: 'This is a test email content.',
      html: '<p>This is a test email content.</p>'
    },
    date: new Date().toISOString(),
    attachments: []
  },
  envelope: {
    messageId: `test-${Date.now()}@example.com`,
    xMailer: 'Test Mailer',
    xOriginalTo: '<EMAIL>',
    returnPath: '<EMAIL>',
    allRecipients: {
      to: [{ name: null, email: '<EMAIL>' }],
      cc: [],
      bcc: []
    },
    headers: {
      'received': ['from example.com by mail.server.com'],
      'message-id': [`test-${Date.now()}@example.com`]
    },
    processed: {
      timestamp: new Date().toISOString(),
      domain: 'in.xadi.nl',
      alias: '<EMAIL>',
      originalSize: 1024
    }
  }
};

async function sendTestPayload(payload, description) {
  console.log(`\n🚀 Sending ${description}...`);
  console.log('Payload:', JSON.stringify(payload, null, 2));
  
  try {
    const response = await axios.post(WEBHOOK_URL, payload, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'EmailConnect/1.0',
        'X-Email-Webhook': 'true'
      },
      timeout: 10000
    });
    
    console.log(`✅ ${description} sent successfully!`);
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error(`❌ Failed to send ${description}:`, error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

async function main() {
  console.log('🔍 EmailConnect Webhook Payload Tester');
  console.log('=====================================');
  
  if (WEBHOOK_URL === 'https://your-n8n-webhook-url-here') {
    console.error('❌ Please update WEBHOOK_URL in the script with your actual n8n webhook URL');
    process.exit(1);
  }
  
  console.log('Target webhook URL:', WEBHOOK_URL);
  
  // Test 1: Send verification payload (what you're currently seeing)
  await sendTestPayload(VERIFICATION_PAYLOAD, 'Verification Payload');
  
  // Wait a bit between tests
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test 2: Send email payload (what you should be seeing for actual emails)
  await sendTestPayload(EMAIL_PAYLOAD, 'Email Payload');
  
  console.log('\n📋 Summary:');
  console.log('- Verification payloads have type: "webhook_verification"');
  console.log('- Email payloads have message and envelope objects');
  console.log('- Your n8n workflow should handle these differently');
  console.log('- Check your n8n execution logs to see which type you\'re receiving');
}

// Handle command line arguments
if (process.argv.length > 2) {
  const webhookUrl = process.argv[2];
  if (webhookUrl.startsWith('http')) {
    WEBHOOK_URL = webhookUrl;
    console.log('Using webhook URL from command line:', webhookUrl);
  }
}

main().catch(console.error);
