# Production Environment Variables
# Copy this file to .env.prod and update with your production values

NODE_ENV=production
PORT=3000
HOST=0.0.0.0
URL=https://emailconnect.eu

# Database Configuration
DB_HOST=localhost
DB_USER=miwh_user
DB_PASSWORD=CHANGE_ME_SECURE_PASSWORD
DB_NAME=miwh_db
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@postgres:5432/${DB_NAME}

# Redis Configuration
REDIS_URL=redis://redis:6379

# Authentication
USER_JWT_SECRET=your-very-strong-and-secret-key-for-user-jwt
USER_JWT_EXPIRES_IN=7d

## Admin authentication
ADMIN_USERNAME=admin
ADMIN_PASSWORD=changeme # IMPORTANT: Use a strong, hashed password in production
ADMIN_EMAIL=<EMAIL>
ADMIN_JWT_SECRET=another-super-secret-admin-jwt-key-change-this
ADMIN_JWT_EXPIRES_IN=1h

# Email Processing Configuration
SPAMC_PATH=/usr/bin/spamc
SPAMC_TIMEOUT_MS=10000
MAX_EMAIL_SIZE_MB=25
MAX_INLINE_ATTACHMENT_SIZE_KB=128
WEBHOOK_TIMEOUT_MS=30000
WEBHOOK_RETRY_ATTEMPTS=3

# DNS Verification Configuration
DNS_VERIFICATION_TIMEOUT_MS=5000
DNS_VERIFICATION_CACHE_TTL_MS=300000
DNS_VERIFICATION_RETRY_ATTEMPTS=3

# GDPR Compliance Configuration
EMAIL_RETENTION_DAYS=30
LOG_RETENTION_DAYS=90

# Usage tracking and billing settings
DEFAULT_MONTHLY_EMAIL_LIMIT=50
FREE_PLAN_EMAIL_LIMIT=50
PRO_PLAN_EMAIL_LIMIT=1000
ENTERPRISE_PLAN_EMAIL_LIMIT=10000

# Payment processing (Mollie)
MOLLIE_API_KEY=live_dHa...
MOLLIE_WEBHOOK_URL=https://yourdomain.com/api/webhooks/mollie
MOLLIE_WEBHOOK_SECRET=your-mollie-prod-webhook-secret-key
MOLLIE_TEST_MODE=true
MOLLIE_PROFILE_ID=pfl_...

# Production Deployment Settings
COMPOSE_PROJECT_NAME=miwh-email-webhook
COMPOSE_FILE=docker-compose.prod.yml
